#!/usr/bin/env node

/**
 * 测试 Outlook 日历 MCP 服务
 * 直接测试我们修改后的 calendar.js 模块
 */

const path = require('path')

// 模拟 Electron app 环境
try {
  const { app } = require('electron')
  if (!app.isPackaged) {
    // 开发环境
    process.env.NODE_ENV = 'development'
  } else {
    // 生产环境
    process.env.NODE_ENV = 'production'
  }
} catch (error) {
  // 在非 Electron 环境中运行，设置为开发环境
  console.log('🧪 在非 Electron 环境中运行，设置为开发环境')
  process.env.NODE_ENV = 'development'

  // 模拟 Electron app 对象
  global.app = {
    isPackaged: process.argv.includes('--production'), // 通过命令行参数控制
    getAppPath: () => process.cwd(),
    getPath: (name) => {
      if (name === 'userData') return path.join(process.cwd(), 'userData')
      return process.cwd()
    }
  }

  // 如果是生产模式，设置相应的环境变量
  if (global.app.isPackaged) {
    process.env.NODE_ENV = 'production'
    // 模拟生产环境的 resourcesPath
    process.resourcesPath = path.join(process.cwd(), 'dist', 'win-unpacked', 'resources')
  }
}

// 导入我们修改后的 calendar.js 模块
const calendarMCP = require('./src/main/mcp/calendar.js')

// 模拟 MCP 管理器
class MockMCPManager {
  constructor() {
    this.clients = new Map()
  }
}

async function testOutlookCalendarMCP() {
  console.log('🧪 开始测试 Outlook 日历 MCP 服务...')
  console.log('🧪 当前环境:', process.env.NODE_ENV)
  console.log('🧪 当前平台:', process.platform)
  
  try {
    // 创建模拟的 MCP 管理器
    const mockMCPManager = new MockMCPManager()
    
    // 初始化 Outlook 日历 MCP 服务
    console.log('🧪 正在初始化 Outlook 日历 MCP 服务...')
    await calendarMCP.initialize(mockMCPManager)
    
    console.log('✅ Outlook 日历 MCP 服务初始化成功！')
    
    // 检查连接状态
    const status = calendarMCP.getConnectionStatus()
    console.log('📊 连接状态:', status)
    
    // 获取可用工具
    const tools = calendarMCP.getAvailableTools()
    console.log('🛠️ 可用工具:', tools)
    
    // 测试工具调用（如果有可用工具）
    if (tools.length > 0) {
      console.log('🧪 测试工具调用...')
      
      // 测试获取日历列表
      if (tools.some(tool => tool.name === 'get_calendars')) {
        try {
          console.log('🧪 测试获取日历列表...')
          const result = await calendarMCP.callTool('get_calendars', {})
          console.log('📅 日历列表结果:', result)
        } catch (error) {
          console.error('❌ 获取日历列表失败:', error.message)
        }
      }
      
      // 测试创建事件
      if (tools.some(tool => tool.name === 'create_event')) {
        try {
          console.log('🧪 测试创建日历事件...')
          const testEvent = {
            subject: '测试事件',
            start: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1小时后
            end: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2小时后
            body: '这是一个测试事件',
            location: '测试地点',
            reminder: 15 // 15分钟提醒
          }
          
          const result = await calendarMCP.callTool('create_event', testEvent)
          console.log('📅 创建事件结果:', result)
        } catch (error) {
          console.error('❌ 创建事件失败:', error.message)
        }
      }
    }
    
    console.log('✅ 测试完成！')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    console.error('❌ 错误堆栈:', error.stack)
  } finally {
    // 清理资源
    try {
      await calendarMCP.cleanup()
      console.log('🧹 资源清理完成')
    } catch (cleanupError) {
      console.error('❌ 资源清理失败:', cleanupError)
    }
    
    // 退出进程
    process.exit(0)
  }
}

// 运行测试
if (require.main === module) {
  testOutlookCalendarMCP().catch(console.error)
}

module.exports = { testOutlookCalendarMCP }
